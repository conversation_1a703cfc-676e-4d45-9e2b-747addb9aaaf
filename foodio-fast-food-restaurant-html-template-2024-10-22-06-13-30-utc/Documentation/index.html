<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" href="style.css" />
	<link href="http://fonts.googleapis.com/css?family=Roboto:100,100italic,300,300italic,400,400italic,500,500italic,700,700italic|Raleway:400,100,200,300,500,600,700,800,900|Noto+Sans:400,400italic,700,700italic|Open+Sans:300,300italic,400,400italic,600,600italic,700,700italic,800,800italic" rel="stylesheet" type="text/css">
	<script src="jquery-1.7.2.min.js"></script>
	<script>
		//smooth scroll by id 
		$(document).ready(function() {
			function filterPath(string) {
				return string
				.replace(/^\//,"")
				.replace(/(index|default).[a-zA-Z]{3,4}$/,"")
				.replace(/\/$/,"");
			}
			var locationPath = filterPath(location.pathname);
			var scrollElem = scrollableElement("html", "body");
			
			$("a[href*=#]").each(function() {
				var thisPath = filterPath(this.pathname) || locationPath;
				if (  locationPath == thisPath
					&& (location.hostname == this.hostname || !this.hostname)
					&& this.hash.replace(/#/,"") ) {
					var $target = $(this.hash), target = this.hash;
				if (target) {
					var targetOffset = $target.offset().top;
					$(this).click(function(event) {
						event.preventDefault();
						$(scrollElem).animate({scrollTop: targetOffset}, 900, function() {
							location.hash = target;
						});
					});
				}
			}
		});
			
  // use the first element that is "scrollable"
  function scrollableElement(els) {
  	for (var i = 0, argLength = arguments.length; i <argLength; i++) {
  		var el = arguments[i],
  		$scrollElement = $(el);
  		if ($scrollElement.scrollTop()> 0) {
  			return el;
  		} else {
  			$scrollElement.scrollTop(1);
  			var isScrollable = $scrollElement.scrollTop()> 0;
  			$scrollElement.scrollTop(0);
  			if (isScrollable) {
  				return el;
  			}
  		}
  	}
  	return [];
  }
  
  
  $(function() {
  	$("#accordion .content").hide();
  	$("#accordion h2:first").addClass("active").next().slideDown("slow");
  	$("#accordion h2").click(function() {
  		if($(this).next().is(":hidden")) {
  			$("#accordion h2").removeClass("active").next().slideUp("slow");
  			$(this).toggleClass("active").next().slideDown("slow");
  		}
  	});
  });

});

</script>
<title>Documentation</title>
</head>

<body>
	<div class="left-side-menu">
		<ul>
			<li><a href="#block1" title="">Introduction</a></li>
			<li><a href="#block2" title="">The Package Includes</a></li>
			<li><a href="#block3" title="">Prominent Features</a></li>
			<li><a href="#block4" title="">Javasripts</a></li>
			<li><a href="#block5" title="">Css Files</a></li>
			<li><a href="#block6" title="">Code Structure</a></li>
			<li><a href="#block7" title="">How to</a></li>
			<li><a href="#block8" title="">Credits</a></li>
		</ul>
	</div>

	<div class="container">
		<div class="top-box">
			<div class="title">
				<h1 id="top">Foodio - Fast Food & Restaurant HTML Template</h1>
			</div>
			<div class="meta">
				Premium Html5 Templates and WP Themes by <a href="https://themeforest.net/user/winsfolio">Winsfolio.net</a> 
			</div>
		</div>
		<div class="content">
			<div class="section">			
				<div class="title">
					<h2 id="block1">Introduction:</h2>
				</div>
				<div class="title">
					<p>Foodio - Fast Food & Restaurant HTML5 Template - Food Retail Screenshots Foodio is a completely new and unique Idea of design for restaurants and online food ordering websites. In this PSD template, you will get fully creative elements that can help you to create your desired website
					</p>
					
					<h2>Our Motto:</h2>
					<p>Every single product launched by the professionally experts. Winsfolio Team is planned and designed after extensive and laborious research work, thus bringing ultimate novelty to address the core issues effectively. The sole determination of our highly skilled and devoted team workers is to bring innovation in the field of HTML template & WordPress theme development that will provide the users with something that they would have never experienced ever before. While the website designing is becoming a promising business, there are also many serious and complex issues that are being faced by the global web community. The same, otherwise disappointing, problems are being addressed here for the utmost facilitation and convenience of the clients.</p>
				</div>
			</div>  

			<div class="section">			
				<div class="title">
					<h2 id="block2">The Package Includes:</h2>
				</div>
				<ul>
					<li>Restaurants Fast Food, Perfect for Restaurant </li>
					<li>Ultra-fresh and flexible layout. Well-commented, fully streamlined and differentiated coding for easier editing and personalization.</li>
					<li>3 outstanding, creative & research-based homepages layouts with extreme level of flexibility and convenience in customization.</li>
					<li>Perfect for all pizza & fast food businesses</li>
					<li>Edge-to-edge responsiveness for a befitting display on all devices.</li>
					<li>Built for Fastfood, Sushi & Pizza Restaurants</li>
					<li>Advanced SEO practices & lightweight for out-and-out search engine visibility.</li>
					<li>Complete step-by-step and visually guiding documentation.</li>
					<li>Free 5-star customer support for 6 months. </li>
					<li>Regular template updates.</li>
				</ul>
			</div>
			<div class="section">			
				<div class="title">
					<h2 id="block3">Prominent Features:</h2>
				</div>
				<p>
                	<strong>Foodio Website Package:</strong>
                     Foodio is a visually impressive, creative and vibrant, well structured, feature rich and responsive HTML5 Template
				</p>
				
				<p>
                	<strong>3 Fresh & Practical Homepage Designs: </strong> 
                In addition to being unique and powerful, the 3 predefined homepages are ultra-fresh in design with immediate practical appeal. Each has a dynamic design with an unparalleled combination and arrangement of all the relevant features for your website. You can also further customize and personalize them with as much quickness and convenience as associated with a WordPress theme.
				</p>
                    
				<p>
                	<strong>Fully Streamlined, Differentiated Coding: </strong> 
                    : The coding of the template is based on high standards and top-line professionalism. Special focus on minimalism makes it considerably lightweight for smoother and quicker page loading. Meanwhile, the streamlined and differentiated coding contributes to the convenient and interactive editing of the features and sections.</p>
                    
            
                    
			</div>	
            		
		  <div class="section">			
				<div class="title">
					<h2 id="block4">Javasripts:</h2>
				</div>
				<p><strong>bootstrap.min.js:</strong>It is a Bootstrap file with the latest version (v5.0). Certain elements (such as tabs) in the template have been designed with the help of this file.</p>
				<p><strong>jquery.min.js:</strong>This particular jQuery file renders functionality to all the other jQuery files featured in the Foodio HTML template.</p>				
				<p><strong>aos.js:</strong>This is animate on scroll js file. You can easily animate your desired section with the help of aos.js</p>
				<p><strong>jquery.counterup.min.js:</strong>This plugin is used to add animation on funfacts.</p>
				<p><strong>jquery.fancybox.min.js:</strong>This plugin is used to open an image in popup</p>
				<p><strong>slick.min.js:</strong>This plugin is used for creating awesome slider</p>	
				<p><strong>Custom.js:</strong>It contains all the jQuery functions that have been incorporated in the template. It is well managed and properly commented.</p>			

			</div>
			<div class="section">			
				<div class="title">
					<h2 id="block5">Css Files:</h2>
				</div>
				<p><strong>style.css</strong>This is the main style file on which the overall styling of the template is based.</p>
				<p><strong>responsive.css</strong>This CSS file gets automatically activated when you are using the template on smaller screen sizes. </p>
				<p><strong>bootstrap.min.css</strong>As the template has been developed on ‘bootstrap’, all the styles of this framework are based on this CSS file.</p>
				<p><strong>animate.min.css</strong>The style for adding unique animation on pages.</p>
				<p><strong>all.min.css</strong>The style for fontawesome icons.</p>
				<p><strong>color.css</strong>As the name suggests, the file belongs to the colour selection.</p>
				<p><strong>Slick.css</strong>This is very necessery file for making sliders for the webpages.</p>
				<p><strong>Animate.min.css</strong>With this file, We can apply very attractive animation effects on the section of the website.</p>
				<p><strong>Fancybox.min.css</strong>This file is use for showing images and videos in popup.</p>
			</div>   
			
			<div class="section">			
				<div class="title">
					<h2 id="block6">CODE STRUCTURE:</h2>
				</div>
				<div class="screenshot"><img src="images/screenshot9.jpg" alt="" /></div>				
			</div>

			<div class="section">			
				<div class="title">
					<h2 id="block7">How to</h2>
				</div>
				
				<div id="accordion">
					
					<h2>How to manage gaps between the sections?</h2>
					<div class="content">
						<p>For the top and bottom spacing for each section, the class <code>"gap"</code>has been used, which applies 120 pixels spacing to the section both from the top and bottom.</p>
						<p>You can manage the inter-section spacing easily according to your page flow.When you use sections one after another, the spacing between the two sections becomes 120 pixels as the bottom space of the top section and the top space of the bottom section will join, and it will become awkward spacing.To manage this spacing between the consecutive sections use class <code>"no-bottom"</code> with the <code>"gap"</code> class in the section from which you want to avoid top space.</p>
						<p><code>&lt;div class="no-top" </code></p>
						
					</div>

	<h2>How to define your own new color scheme?</h2>
	<div class="content">
		<p>If you wish to change the color scheme of the template, it is very easy to define and apply it. Go to the "assets" folder. You will find "css" folder inside it. In this folder, you can locate "color.css" file. Open it in the editor. You will see a long list of classes separated with commas (,). Here you need to change the css properties for the background, color and border. For this purpose, just change the color code in that file and the color scheme will be changed in the whole template.</p>
	</div>


</div>				
</div>	
<div class="section">			
	<div class="title">
		<h2 id="block8">Credits</h2>
	</div>
	<p>The credit for the development of this marvelous template goes to:</p>
	<ul>
		<li>Google Fonts</li>
		<li>Bootstrap</li>
		<li>Fancybox</li>
		<li>Animation.style</li>
		<li>Slick Slider</li>
		<li>Developers of Javascript files which have been utilized herein. </li>
		<li>Team of Winsfolio.net</li>
	</ul>
</div>  
</div>
</div>
</body>
</html>
